:root {
	--color-primary: hsl(29, 24%, 42%);
	--color-background: hsl(33, 100%, 94%);
	--color-text: hsl(13, 12%, 15%);

	--color-button: hsl(12, 6%, 15%);
	--color-button-text: var(--color-background);

	--color-line-pink: hsl(0, 100%, 78%);

	--color-navigation-background: hsl(30, 55%, 84%);
	--color-navigation-text: var(--color-primary);

	--color-selection: hsla(12, 6%, 15%, 0.1);

	--font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
		Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif,
		"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
		"Noto Color Emoji";
}

*,
*::before,
*::after {
	box-sizing: border-box;
}

::selection {
	background-color: var(--color-selection);
}

* {
	margin: 0;
	padding: 0;
	font: inherit;
}

html {
	color-scheme: dark light;
	font-size: 16px;
}

body {
	height: 100dvh;
	font-family: var(--font-family);
	color: var(--color-text);
	background-color: var(--color-background);
	padding-bottom: 60px;
	overflow-x: hidden; /* Prevent horizontal scroll from animations */
	/* Initial state for page transitions */
}

img,
picture,
svg,
video,
canvas {
	display: block;
	max-width: 100%;
	user-select: none;
	-moz-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;
}

header {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 1.75rem 2rem;
	gap: 8px;
	/* Initial state for animations */
	opacity: 0;
	transform: translateY(-20px);

	& > span {
		font-size: clamp(1.2rem, 4vw, 1.5rem);
		font-family: var(--font-family);
		font-variation-settings: "wdth" 150;
		font-weight: 1000;
		color: var(--color-text);
		text-transform: uppercase;
		letter-spacing: -6%;
	}

	& > svg {
		transition: transform 0.3s ease;
		cursor: pointer;
	}
}

main {
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	min-height: calc(100dvh - 15.688rem);

	& > img {
		position: absolute;
		transform: translate(-50%, -50%);
		top: 50%;
		left: 50%;
		user-select: none;
		pointer-events: none;

		&.blur {
			z-index: -1;
		}

		&.figma {
			width: 10%;
			top: 20%;
			left: 30%;
		}

		&.laptop {
			width: 15%;
			top: 20%;
			left: 70%;
		}

		&.sticker {
			width: 10%;
			top: 70%;
			left: 30%;
		}

		&.mica {
			top: 10%;
			left: 10%;
			scale: 0.8;
		}

		&.neo {
			top: 50%;
			left: 90%;
		}

		&.saku {
			top: 60%;
			left: 10%;
			scale: 0.8;
		}
	}
}

section {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 1rem;
	padding: 2rem;
	/* Initial states for animations */
	& .website-tagline,
	& h1,
	& .hero-text,
	& .header__discord {
		opacity: 0;
		transform: translateY(30px) scale(0.9);
	}

	& .website-tagline {
		display: inline-flex;
		align-items: center;
		font-size: clamp(1rem, 3vw, 1.25rem);
		font-weight: bold;
		color: var(--color-primary);
		letter-spacing: -2%;
	}

	& h1 {
		font-size: clamp(2rem, 5vw, 4rem);
		font-weight: bold;
		color: var(--color-text);
		text-align: center;
	}

	& .hero-text {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 1rem;
		margin-bottom: 1rem;

		& img {
			width: 10%;
			user-select: none;
			pointer-events: none;
		}

		& .stacked-images {
			opacity: 0;
			transform: translateX(-20px) scale(0.9);
		}

		& span {
			font-size: clamp(0.9rem, 2.5vw, 1rem);
			font-weight: medium;
			color: var(--color-text);
			letter-spacing: -2%;

			& mark {
				background: transparent;
				box-shadow: inset 0 -0.3em 0 0 var(--color-line-pink);
			}
		}
	}
}

a {
	display: inline-flex;
	align-items: center;
	gap: 8px;
	font-size: clamp(1rem, 2.5vw, 1.125rem);
	line-height: 1.75rem;
	font-weight: 700;
	text-decoration: none;
	transition: color 0.3s, background 0.3s;
	padding: clamp(0.8rem, 2vw, 1rem) clamp(1rem, 2.5vw, 1.25rem);
	border-radius: clamp(0.8rem, 2vw, 1rem);
}

a > .icon {
	margin-right: 0.5rem;
}

a.header__discord {
	color: var(--color-button-text);
	background: var(--color-button);
	padding-block: 16px;
	padding-inline: 52px 20px;
	position: relative;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	will-change: transform; /* Optimize for animations */
}

a.header__discord > .icon.discord {
	position: absolute;
	left: 20px;
	background-repeat: no-repeat;
	background-position: center;
	background-size: contain;
	opacity: 1;
	width: 19px;
	height: 19px;
	transform: scale(1.2);
	transition: opacity 0.3s, left 0.3s,
		transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

a.header__discord > .label-wrapper {
	display: flex;
	align-items: center;
	gap: 8px;
}

a.header__discord > .label-wrapper > .text {
	font-size: clamp(1rem, 2.5vw, 1.125rem);
	line-height: 1.75rem;
	font-weight: 700;
	color: var(--color-button-text);
}

a.header__discord > .label-wrapper > .icon.arrow {
	position: absolute;
	background-repeat: no-repeat;
	background-position: center;
	background-size: contain;
	opacity: 0;
	width: 16px;
	height: 16px;
	transform: scale(0) rotate(180deg); /* Start at scale 0 */
	transition: opacity 0.3s, right 0.3s,
		transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	visibility: hidden; /* Hide by default */
}

a.header__discord:hover {
	padding-inline: 20px 52px;
}

a.header__discord:hover > .icon.discord {
	opacity: 0;
	left: 0px;
}

a.header__discord:hover > .label-wrapper > .icon.arrow {
	opacity: 1;
	right: -25px;
	transform: rotate(360deg) scale(2);
	visibility: visible;
}

a:hover {
	background: var(--color-button-hover);
}

a.button-primary {
	background: var(--color-button);
	box-shadow: 0px 1.67px 23.13px 0 hsla(28, 100%, 66%, 0.15),
		0 104.58px 29.17px 0 hsla(0, 0%, 0%, 0),
		0 67.08px 26.67px 0 hsla(0, 0%, 0%, 0.03),
		0 37.5px 22.5px 0 hsla(0, 0%, 0%, 0.1),
		0 16.67px 16.67px 0 hsla(0, 0%, 0%, 0.16),
		0 4.17px 9.17px 0 hsla(0, 0%, 0%, 0.19);
}

a.button-primary > .label-wrapper > .text {
	color: var(--color-button-text);
}

nav {
	--color-navigation-background: 30 55% 84%;
	--color-navigation-text: var(--color-primary);
	--color-navigation-active-background: 12 6% 15%;
	--color-navigation-active-text: 30 8% 95%;

	position: fixed;
	bottom: -20%;
	left: 50%;
	transform: translateX(-50%);
	z-index: 9999;

	display: flex;
	justify-content: center;
	align-items: center;
	gap: 5px;
	border-radius: 16px;
	padding: 6px;
	margin: 0;

	background-color: hsl(var(--color-navigation-background));
	/* Initial state for animations */
	opacity: 0;
	backdrop-filter: blur(10px);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

nav a {
	color: hsl(var(--color-navigation-text));
	text-decoration: none;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	padding: clamp(4px, 1vw, 6px) clamp(8px, 2vw, 10px);
	border-radius: clamp(8px, 2vw, 10px);
	gap: clamp(3px, 1vw, 4px);
	margin-top: 0;
	font-weight: 600;
	letter-spacing: -0.43px;
	line-height: 22%;
	font-size: clamp(0.7rem, 2vw, 0.8rem);
	cursor: pointer;
	height: clamp(26px, 6vw, 30px);
	display: flex;
	align-items: center;
	justify-content: center;
}

nav a.active {
	background-color: hsl(var(--color-navigation-active-background));
	color: hsl(var(--color-navigation-active-text));
	/* Height and alignment already set in base nav a styles */
}

nav a:hover {
	background-color: hsla(var(--color-navigation-active-background) / 0.7);
	color: hsl(var(--color-navigation-active-text));

	& svg path {
		fill: hsl(var(--color-navigation-active-text));
	}
}

.heart {
	position: fixed;
	top: 100%;
	left: 60%;
	transform: translateX(-50%);
	z-index: 9999;
	width: 40px;
	opacity: 1;
	transition: opacity 0.3s ease;
	pointer-events: none;
}

.project-container {
	display: flex;
	flex-wrap: wrap;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	gap: 1rem;
	position: relative;
	padding: 2rem;
	margin: 0 auto;
	max-width: 1200px;
	width: 90%;
}

.project {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: center;
	width: 100%;
	max-width: 300px;
	margin: 0 auto;
	/* Initial state for animations */
	opacity: 0;
	transform: translateY(50px) scale(0.9);
	cursor: pointer;
	transition: transform 0.3s ease;
	will-change: transform; /* Optimize for animations */
}

.project-banner {
	border-radius: 1rem;
	transform: rotate(4deg);
	object-fit: cover;
	object-position: center;
	height: auto;
	max-height: 200px;
	user-select: none;
	pointer-events: none;
	transition: transform 0.3s ease;
	will-change: transform; /* Optimize for animations */
	/* Initial state for animations */
	opacity: 0;
}

.project-content {
	display: flex;
	flex-direction: column;
	align-items: start;
	gap: 0.25rem;
}

.project-icon {
	width: 25px;
	user-select: none;
	pointer-events: none;
	/* Initial state for animations */
	opacity: 0;
	transform: translateY(20px) scale(0.8);
}

.project-name {
	font-size: clamp(1rem, 5vw, 1.5rem);
	font-weight: bold;
	color: var(--color-text);
	text-align: center;
	letter-spacing: -2%;
	/* Initial state for animations */
	opacity: 0;
	transform: translateY(20px) scale(0.9);
}

.project-description {
	font-size: clamp(0.75rem, 3vw, 0.875rem);
	font-weight: medium;
	color: var(--color-text);
	letter-spacing: -1%;
	/* Initial state for animations - will be set to 0.4 after animation */
	opacity: 0;
	transform: translateY(20px) scale(0.9);
}

.project-image {
	width: 30%;
	position: absolute;
	bottom: 50px;
	right: 0;
	user-select: none;
	pointer-events: none;
	/* Initial state for animations */
	opacity: 0;
	transform: translateX(20px) scale(0.8);
}

/* Animation performance optimizations */
* {
	/* Enable hardware acceleration for better animation performance */
	transform: translateZ(0);
	backface-visibility: hidden;
	perspective: 1000px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
	*,
	*::before,
	*::after {
		animation-duration: 0.01ms !important;
		animation-iteration-count: 1 !important;
		transition-duration: 0.01ms !important;
		scroll-behavior: auto !important;
	}
}

/* Enhanced focus states for accessibility */
a:focus-visible,
button:focus-visible {
	outline: 2px solid var(--color-primary);
	outline-offset: 2px;
	border-radius: 4px;
}

/* Smooth scrolling */
html {
	scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: var(--color-background);
}

::-webkit-scrollbar-thumb {
	background: var(--color-primary);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: var(--color-text);
}

/* Clients page styles */
.clients-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	gap: clamp(2rem, 5vw, 4rem);
	padding: clamp(1rem, 4vw, 2rem);
	height: 100%;
	max-width: 800px;
	margin: 0 auto;
	flex-wrap: wrap;
}

@media (max-width: 768px) {
	.clients-container {
		flex-direction: column;
		gap: 2rem;
	}
}

.client-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: clamp(0.5rem, 2vw, 1rem);
	opacity: 0;
	transform: translateY(30px) scale(0.9);
	cursor: pointer;
	transition: transform 0.3s ease;
	min-width: 120px;
	flex: 1;
	max-width: 200px;
}

.client-icon {
	width: clamp(60px, 15vw, 80px);
	height: clamp(60px, 15vw, 80px);
	border-radius: clamp(15px, 4vw, 20px);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
	transition: transform 0.3s ease;
}

.client-icon.blitzforge {
	background: #1a1a1a;
}

.client-icon.community {
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.client-info {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 0.5rem;
	position: relative;
}

.client-name {
	font-size: clamp(1rem, 3vw, 1.2rem);
	font-weight: 600;
	color: var(--color-text);
	text-align: center;
	margin: 0;
}

.become-partner {
	display: flex;
	align-items: center;
	gap: clamp(0.3rem, 1vw, 0.5rem);
	padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 3vw, 1.5rem);
	background: rgba(0, 0, 0, 0.05);
	border-radius: 13px;
	margin-top: clamp(1rem, 4vw, 2rem);
	opacity: 0;
	transform: translateY(20px);
	cursor: pointer;
	transition: all 0.3s ease;
}

.become-partner:hover {
	background: rgba(0, 0, 0, 0.1);
	transform: translateY(-2px);
}

.partner-text {
	font-size: clamp(0.8rem, 2vw, 0.9rem);
	font-weight: 600;
	color: var(--color-text);
}

.no-limit-badge {
	background: var(--color-text);
	color: var(--color-background);
	padding: clamp(0.2rem, 1vw, 0.25rem) clamp(0.4rem, 1vw, 0.5rem);
	border-radius: 6px;
	font-size: clamp(0.6rem, 1.5vw, 0.7rem);
	font-weight: 700;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

/* Navigation override for clients page */
.nav-override nav {
	background-color: var(--color-text);
}

.nav-override nav a {
	color: var(--color-background);
}

.nav-override nav a.active {
	background-color: #ff69b4;
	color: var(--color-text);
}

.nav-override nav a:hover {
	background-color: rgba(255, 105, 180, 0.7);
	color: var(--color-text);
}

.nav-override nav a:hover svg path {
	fill: var(--color-text);
}
